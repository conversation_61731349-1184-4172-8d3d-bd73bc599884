Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to serial.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    timer.o(i.Timer1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    timer.o(i.Timer1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.Timer6_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.Timer6_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer6_Init) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    timer.o(i.Timer6_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.Timer6_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.Timer6_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    key.o(i.Key_GetNum) refers to key.o(.data) for Key_Num
    key.o(i.Key_GetState) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_Tick) refers to key.o(i.Key_GetState) for Key_GetState
    key.o(i.Key_Tick) refers to key.o(.data) for Count
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to dadd.o(.text) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(.text) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to cfcmple.o(.text) for __aeabi_cfcmple
    oled.o(i.OLED_DrawEllipse) refers to fflti.o(.text) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to fadd.o(.text) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to cdcmple.o(.text) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(.text) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfixi.o(.text) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixui.o(.text) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dfltui.o(.text) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to dadd.o(.text) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to strcmp.o(.text) for strcmp
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowString) refers to oled_data.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_W_SCL) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_W_SDA) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    rp.o(i.RP_GetValue) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    rp.o(i.RP_GetValue) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    rp.o(i.RP_GetValue) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    rp.o(i.RP_GetValue) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    rp.o(i.RP_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    rp.o(i.RP_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    rp.o(i.RP_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    rp.o(i.RP_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    rp.o(i.RP_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    rp.o(i.RP_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    rp.o(i.RP_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    rp.o(i.RP_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    rp.o(i.RP_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    pwm.o(i.PWM_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    pwm.o(i.PWM_Init) refers to pwm.o(.data) for arr_time2
    encoder.o(i.Encoder_Get1) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Encoder_Get1) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Get2) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Encoder_Get2) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Get3) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Encoder_Get3) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Get4) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Encoder_Get4) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ICStructInit) for TIM_ICStructInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ICInit) for TIM_ICInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    serial.o(i.Serial_GetRxData) refers to serial.o(.data) for Serial_RxData
    serial.o(i.Serial_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    serial.o(i.Serial_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    serial.o(i.Serial_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    serial.o(i.Serial_Printf) refers to serial.o(i.Serial_SendString) for Serial_SendString
    serial.o(i.Serial_SendArray) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_Pow) for Serial_Pow
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendString) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    serial.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    serial.o(i.USART1_IRQHandler) refers to serial.o(.data) for Serial_RxData
    serial.o(i.USART1_IRQHandler) refers to serial.o(.bss) for uart1_rx_buf
    serial.o(i.USART1_IRQHandler) refers to main.o(.data) for blob_cx
    serial.o(i.Uart2Send) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.Uart2Send) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    serial.o(i.fputc) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    pid.o(i.PID_Postion) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(i.PID_Postion) refers to cfcmple.o(.text) for __aeabi_cfcmpeq
    pid.o(i.PID_Postion) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(i.PID_Postion) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    pid.o(i.PID_cal) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(i.PID_cal) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(i.PID_cal) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    pid.o(i.PID_cal) refers to cfcmple.o(.text) for __aeabi_cfcmple
    wit_c_sdk.o(i.CopeWitData) refers to memcpya.o(.text) for __aeabi_memcpy
    wit_c_sdk.o(i.CopeWitData) refers to wit_c_sdk.o(.data) for s_uiReadRegIndex
    wit_c_sdk.o(i.CopeWitData) refers to wit_c_sdk.o(.bss) for sReg
    wit_c_sdk.o(i.WitCaliRefAngle) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitCaliRefAngle) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(i.CopeWitData) for CopeWitData
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(.data) for p_WitRegUpdateCbFunc
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(.bss) for sReg
    wit_c_sdk.o(i.WitCanWriteRegister) refers to wit_c_sdk.o(.data) for p_WitCanWriteFunc
    wit_c_sdk.o(i.WitDeInit) refers to wit_c_sdk.o(.data) for p_WitSerialWriteFunc
    wit_c_sdk.o(i.WitDelayMsRegister) refers to wit_c_sdk.o(.data) for p_WitDelaymsFunc
    wit_c_sdk.o(i.WitI2cFuncRegister) refers to wit_c_sdk.o(.data) for p_WitI2cWriteFunc
    wit_c_sdk.o(i.WitInit) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(.bss) for s_ucWitDataBuff
    wit_c_sdk.o(i.WitRegisterCallBack) refers to wit_c_sdk.o(.data) for p_WitRegUpdateCbFunc
    wit_c_sdk.o(i.WitSaveParameter) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSaveParameter) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitSerialDataIn) refers to memcpya.o(.text) for __aeabi_memcpy
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.__CaliSum) for __CaliSum
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.CopeWitData) for CopeWitData
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(.data) for p_WitRegUpdateCbFunc
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(.bss) for s_ucWitDataBuff
    wit_c_sdk.o(i.WitSerialWriteRegister) refers to wit_c_sdk.o(.data) for p_WitSerialWriteFunc
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitSetForReset) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetForReset) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitStartAccCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStartAccCali) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitStartMagCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStartMagCali) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitStopAccCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStopAccCali) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitStopMagCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStopMagCali) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.WitWriteReg) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitWriteReg) refers to wit_c_sdk.o(.data) for s_uiProtoclo
    wit_c_sdk.o(i.__CRC16) refers to wit_c_sdk.o(.constdata) for __auchCRCHi
    bjbj_driver.o(i.Control_Step) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bjbj_driver.o(i.Control_Step) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bjbj_driver.o(i.GPIO_Config) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bjbj_driver.o(i.GPIO_Config) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bjbj_driver.o(i.SubdivisionSet) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bjbj_driver.o(i.SubdivisionSet) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.GPIO_Init_key) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    main.o(i.GPIO_Init_key) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    main.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.TIM2_IRQHandler) refers to main.o(.data) for Step1
    main.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    main.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.TIM3_IRQHandler) refers to main.o(.data) for Step2
    main.o(i.TIM6_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM6_IRQHandler) refers to main.o(i.Key_read) for Key_read
    main.o(i.TIM6_IRQHandler) refers to bjbj_driver.o(i.Control_Step) for Control_Step
    main.o(i.TIM6_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.TIM6_IRQHandler) refers to main.o(.data) for key_count
    main.o(i.key_select) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.key_select) refers to main.o(.data) for key_count
    main.o(i.main) refers to main.o(i.GPIO_Init_key) for GPIO_Init_key
    main.o(i.main) refers to main.o(i.key_select) for key_select
    main.o(i.main) refers to timer.o(i.Timer6_Init) for Timer6_Init
    main.o(i.main) refers to bjbj_driver.o(i.GPIO_Config) for GPIO_Config
    main.o(i.main) refers to bjbj_driver.o(i.SubdivisionSet) for SubdivisionSet
    main.o(i.main) refers to pwm.o(i.PWM_Init) for PWM_Init
    main.o(i.main) refers to serial.o(i.Serial_Init) for Serial_Init
    main.o(i.main) refers to fflti.o(.text) for __aeabi_i2f
    main.o(i.main) refers to pid.o(i.PID_Postion) for PID_Postion
    main.o(i.main) refers to cfcmple.o(.text) for __aeabi_cfcmple
    main.o(i.main) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(i.main) refers to main.o(i.fabs1) for fabs1
    main.o(i.main) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    main.o(i.main) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to main.o(.data) for extern_flag
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_drsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to serial.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing timer.o(i.Timer1_Init), (168 bytes).
    Removing key.o(i.Key_GetNum), (28 bytes).
    Removing key.o(i.Key_GetState), (80 bytes).
    Removing key.o(i.Key_Init), (72 bytes).
    Removing key.o(i.Key_Tick), (80 bytes).
    Removing key.o(.data), (4 bytes).
    Removing led.o(i.LED_Init), (52 bytes).
    Removing led.o(i.LED_OFF), (20 bytes).
    Removing led.o(i.LED_ON), (20 bytes).
    Removing led.o(i.LED_Turn), (44 bytes).
    Removing oled.o(i.OLED_Clear), (40 bytes).
    Removing oled.o(i.OLED_ClearArea), (144 bytes).
    Removing oled.o(i.OLED_DrawArc), (618 bytes).
    Removing oled.o(i.OLED_DrawCircle), (352 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (812 bytes).
    Removing oled.o(i.OLED_DrawLine), (374 bytes).
    Removing oled.o(i.OLED_DrawPoint), (80 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (142 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (232 bytes).
    Removing oled.o(i.OLED_GPIO_Init), (96 bytes).
    Removing oled.o(i.OLED_GetPoint), (68 bytes).
    Removing oled.o(i.OLED_I2C_SendByte), (62 bytes).
    Removing oled.o(i.OLED_I2C_Start), (28 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (22 bytes).
    Removing oled.o(i.OLED_Init), (154 bytes).
    Removing oled.o(i.OLED_IsInAngle), (124 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_Printf), (50 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (144 bytes).
    Removing oled.o(i.OLED_SetCursor), (34 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (70 bytes).
    Removing oled.o(i.OLED_ShowChar), (84 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (210 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (96 bytes).
    Removing oled.o(i.OLED_ShowImage), (252 bytes).
    Removing oled.o(i.OLED_ShowNum), (76 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (112 bytes).
    Removing oled.o(i.OLED_ShowString), (452 bytes).
    Removing oled.o(i.OLED_Update), (40 bytes).
    Removing oled.o(i.OLED_UpdateArea), (124 bytes).
    Removing oled.o(i.OLED_W_SCL), (24 bytes).
    Removing oled.o(i.OLED_W_SDA), (24 bytes).
    Removing oled.o(i.OLED_WriteCommand), (32 bytes).
    Removing oled.o(i.OLED_WriteData), (46 bytes).
    Removing oled.o(i.OLED_pnpoly), (140 bytes).
    Removing oled.o(.bss), (1024 bytes).
    Removing oled_data.o(.constdata), (2381 bytes).
    Removing rp.o(i.RP_GetValue), (108 bytes).
    Removing rp.o(i.RP_Init), (148 bytes).
    Removing encoder.o(i.Encoder_Get1), (28 bytes).
    Removing encoder.o(i.Encoder_Get2), (28 bytes).
    Removing encoder.o(i.Encoder_Get3), (28 bytes).
    Removing encoder.o(i.Encoder_Get4), (26 bytes).
    Removing encoder.o(i.Encoder_Init), (440 bytes).
    Removing serial.o(i.Serial_GetRxData), (12 bytes).
    Removing serial.o(i.Serial_Pow), (20 bytes).
    Removing serial.o(i.Serial_Printf), (36 bytes).
    Removing serial.o(i.Serial_SendArray), (26 bytes).
    Removing serial.o(i.Serial_SendByte), (32 bytes).
    Removing serial.o(i.Serial_SendNumber), (58 bytes).
    Removing serial.o(i.Serial_SendString), (26 bytes).
    Removing serial.o(i.Uart2Send), (60 bytes).
    Removing serial.o(i.fputc), (16 bytes).
    Removing pid.o(i.PID_cal), (146 bytes).
    Removing wit_c_sdk.o(i.CheckRange), (18 bytes).
    Removing wit_c_sdk.o(i.CopeWitData), (240 bytes).
    Removing wit_c_sdk.o(i.WitCaliRefAngle), (84 bytes).
    Removing wit_c_sdk.o(i.WitCanDataIn), (216 bytes).
    Removing wit_c_sdk.o(i.WitCanWriteRegister), (24 bytes).
    Removing wit_c_sdk.o(i.WitDeInit), (80 bytes).
    Removing wit_c_sdk.o(i.WitDelayMsRegister), (24 bytes).
    Removing wit_c_sdk.o(i.WitI2cFuncRegister), (40 bytes).
    Removing wit_c_sdk.o(i.WitInit), (44 bytes).
    Removing wit_c_sdk.o(i.WitReadReg), (472 bytes).
    Removing wit_c_sdk.o(i.WitRegisterCallBack), (24 bytes).
    Removing wit_c_sdk.o(i.WitSaveParameter), (84 bytes).
    Removing wit_c_sdk.o(i.WitSerialDataIn), (492 bytes).
    Removing wit_c_sdk.o(i.WitSerialWriteRegister), (24 bytes).
    Removing wit_c_sdk.o(i.WitSetBandwidth), (88 bytes).
    Removing wit_c_sdk.o(i.WitSetCanBaud), (112 bytes).
    Removing wit_c_sdk.o(i.WitSetContent), (108 bytes).
    Removing wit_c_sdk.o(i.WitSetForReset), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetOutputRate), (104 bytes).
    Removing wit_c_sdk.o(i.WitSetUartBaud), (176 bytes).
    Removing wit_c_sdk.o(i.WitStartAccCali), (128 bytes).
    Removing wit_c_sdk.o(i.WitStartMagCali), (84 bytes).
    Removing wit_c_sdk.o(i.WitStopAccCali), (84 bytes).
    Removing wit_c_sdk.o(i.WitStopMagCali), (84 bytes).
    Removing wit_c_sdk.o(i.WitWriteReg), (336 bytes).
    Removing wit_c_sdk.o(i.__CRC16), (56 bytes).
    Removing wit_c_sdk.o(i.__CaliSum), (26 bytes).
    Removing wit_c_sdk.o(.bss), (544 bytes).
    Removing wit_c_sdk.o(.constdata), (512 bytes).
    Removing wit_c_sdk.o(.data), (40 bytes).
    Removing bjbj_driver.o(i.DelayNop), (26 bytes).
    Removing main.o(i.sort_rect), (2 bytes).
    Removing main.o(.bss), (32 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing depilogue.o(.text), (186 bytes).
    Removing drnd.o(.text), (136 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing dscalb.o(.text), (46 bytes).

565 unused section(s) (total 34989 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    Hardware\Encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\OLED_Data.c                     0x00000000   Number         0  oled_data.o ABSOLUTE
    Hardware\PID.c                           0x00000000   Number         0  pid.o ABSOLUTE
    Hardware\PWM.c                           0x00000000   Number         0  pwm.o ABSOLUTE
    Hardware\RP.c                            0x00000000   Number         0  rp.o ABSOLUTE
    Hardware\Serial.c                        0x00000000   Number         0  serial.o ABSOLUTE
    Hardware\bjbj_driver.c                   0x00000000   Number         0  bjbj_driver.o ABSOLUTE
    Hardware\wit_c_sdk.c                     0x00000000   Number         0  wit_c_sdk.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_hd.s             0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    System\Timer.c                           0x00000000   Number         0  timer.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000140   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000144   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000144   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000144   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000144   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000148   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800016c   Section        0  fadd.o(.text)
    .text                                    0x0800021c   Section        0  fmul.o(.text)
    .text                                    0x08000280   Section        0  fflti.o(.text)
    .text                                    0x08000292   Section        0  ffixi.o(.text)
    .text                                    0x080002c4   Section       20  cfcmple.o(.text)
    .text                                    0x080002d8   Section       20  cfrcmple.o(.text)
    .text                                    0x080002ec   Section        0  iusefp.o(.text)
    .text                                    0x080002ec   Section        0  fepilogue.o(.text)
    .text                                    0x0800035c   Section       36  init.o(.text)
    i.BusFault_Handler                       0x08000380   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Control_Step                           0x08000384   Section        0  bjbj_driver.o(i.Control_Step)
    i.DebugMon_Handler                       0x080003e0   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080003e2   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x080003fa   Section        0  delay.o(i.Delay_us)
    i.GPIO_Config                            0x08000428   Section        0  bjbj_driver.o(i.GPIO_Config)
    i.GPIO_Init                              0x080004a4   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_Init_key                          0x080005bc   Section        0  main.o(i.GPIO_Init_key)
    i.GPIO_ReadInputDataBit                  0x080005e0   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x080005f2   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080005f6   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x080005fa   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_read                               0x080005fe   Section        0  main.o(i.Key_read)
    i.MemManage_Handler                      0x08000602   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000606   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000608   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000678   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PID_Postion                            0x0800068c   Section        0  pid.o(i.PID_Postion)
    i.PWM_Init                               0x08000710   Section        0  pwm.o(i.PWM_Init)
    i.PendSV_Handler                         0x08000824   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000828   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000848   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000868   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x0800093c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Serial_Init                            0x08000940   Section        0  serial.o(i.Serial_Init)
    i.SetSysClock                            0x080009f4   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080009f5   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080009fc   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080009fd   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SubdivisionSet                         0x08000adc   Section        0  bjbj_driver.o(i.SubdivisionSet)
    i.SysTick_Handler                        0x08000b0c   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000b10   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08000b70   Section        0  main.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08000ba8   Section        0  main.o(i.TIM3_IRQHandler)
    i.TIM6_IRQHandler                        0x08000be4   Section        0  main.o(i.TIM6_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x08000d14   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearFlag                          0x08000d2c   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_ClearITPendingBit                  0x08000d32   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08000d38   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08000d50   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08000d72   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC1Init                            0x08000d84   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x08000e1c   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_TimeBaseInit                       0x08000e30   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Timer6_Init                            0x08000ed4   Section        0  timer.o(i.Timer6_Init)
    i.USART1_IRQHandler                      0x08000f44   Section        0  serial.o(i.USART1_IRQHandler)
    i.USART_ClearITPendingBit                0x08001054   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001072   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x0800108a   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080010de   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001128   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001200   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x0800120a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x0800120e   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800121c   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800121e   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.fabs1                                  0x0800122c   Section        0  main.o(i.fabs1)
    i.key_select                             0x0800123c   Section        0  main.o(i.key_select)
    i.main                                   0x08001324   Section        0  main.o(i.main)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section        8  pwm.o(.data)
    .data                                    0x2000001c   Section        7  serial.o(.data)
    last_processed_pos                       0x2000001c   Data           4  serial.o(.data)
    .data                                    0x20000024   Section      181  main.o(.data)
    last_processed_pos                       0x20000024   Data           4  main.o(.data)
    .bss                                     0x200000dc   Section       32  serial.o(.bss)
    STACK                                    0x20000100   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000141   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000145   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000145   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000149   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_fadd                             0x0800016d   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08000211   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08000217   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x0800021d   Thumb Code   100  fmul.o(.text)
    __aeabi_i2f                              0x08000281   Thumb Code    18  fflti.o(.text)
    __aeabi_f2iz                             0x08000293   Thumb Code    50  ffixi.o(.text)
    __aeabi_cfcmpeq                          0x080002c5   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x080002c5   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x080002d9   Thumb Code    20  cfrcmple.o(.text)
    __I$use$fp                               0x080002ed   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080002ed   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080002ff   Thumb Code    92  fepilogue.o(.text)
    __scatterload                            0x0800035d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800035d   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x08000381   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Control_Step                             0x08000385   Thumb Code    86  bjbj_driver.o(i.Control_Step)
    DebugMon_Handler                         0x080003e1   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080003e3   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x080003fb   Thumb Code    46  delay.o(i.Delay_us)
    GPIO_Config                              0x08000429   Thumb Code   116  bjbj_driver.o(i.GPIO_Config)
    GPIO_Init                                0x080004a5   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_Init_key                            0x080005bd   Thumb Code    32  main.o(i.GPIO_Init_key)
    GPIO_ReadInputDataBit                    0x080005e1   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x080005f3   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080005f7   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x080005fb   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_read                                 0x080005ff   Thumb Code     4  main.o(i.Key_read)
    MemManage_Handler                        0x08000603   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08000607   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000609   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000679   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PID_Postion                              0x0800068d   Thumb Code   132  pid.o(i.PID_Postion)
    PWM_Init                                 0x08000711   Thumb Code   258  pwm.o(i.PWM_Init)
    PendSV_Handler                           0x08000825   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000829   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000849   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000869   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x0800093d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Serial_Init                              0x08000941   Thumb Code   172  serial.o(i.Serial_Init)
    SubdivisionSet                           0x08000add   Thumb Code    38  bjbj_driver.o(i.SubdivisionSet)
    SysTick_Handler                          0x08000b0d   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08000b11   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08000b71   Thumb Code    52  main.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08000ba9   Thumb Code    50  main.o(i.TIM3_IRQHandler)
    TIM6_IRQHandler                          0x08000be5   Thumb Code   236  main.o(i.TIM6_IRQHandler)
    TIM_ARRPreloadConfig                     0x08000d15   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearFlag                            0x08000d2d   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_ClearITPendingBit                    0x08000d33   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08000d39   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08000d51   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08000d73   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC1Init                              0x08000d85   Thumb Code   132  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x08000e1d   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_TimeBaseInit                         0x08000e31   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Timer6_Init                              0x08000ed5   Thumb Code   106  timer.o(i.Timer6_Init)
    USART1_IRQHandler                        0x08000f45   Thumb Code   240  serial.o(i.USART1_IRQHandler)
    USART_ClearITPendingBit                  0x08001055   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001073   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x0800108b   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080010df   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001129   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001201   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x0800120b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x0800120f   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800121d   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800121f   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fabs1                                    0x0800122d   Thumb Code    14  main.o(i.fabs1)
    key_select                               0x0800123d   Thumb Code   198  main.o(i.key_select)
    main                                     0x08001325   Thumb Code   296  main.o(i.main)
    Region$$Table$$Base                      0x0800147c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800149c   Number         0  anon$$obj.o(Region$$Table)
    arr_time2                                0x20000014   Data           4  pwm.o(.data)
    arr_time3                                0x20000018   Data           4  pwm.o(.data)
    uart1_rx_idx                             0x20000020   Data           1  serial.o(.data)
    uart1_rx_state                           0x20000021   Data           1  serial.o(.data)
    Serial_RxData                            0x20000022   Data           1  serial.o(.data)
    location                                 0x20000028   Data           1  main.o(.data)
    location_old                             0x20000029   Data           1  main.o(.data)
    key_value                                0x2000002a   Data           1  main.o(.data)
    key_old                                  0x2000002b   Data           1  main.o(.data)
    key_down                                 0x2000002c   Data           1  main.o(.data)
    key_count                                0x2000002d   Data           1  main.o(.data)
    step1_flag                               0x2000002e   Data           1  main.o(.data)
    step2_flag                               0x2000002f   Data           1  main.o(.data)
    flag                                     0x20000030   Data           4  main.o(.data)
    Step1                                    0x20000034   Data          12  main.o(.data)
    Step2                                    0x20000040   Data          12  main.o(.data)
    STEP_X_PID                               0x2000004c   Data          48  main.o(.data)
    STEP_Y_PID                               0x2000007c   Data          48  main.o(.data)
    blob_cx                                  0x200000ac   Data           4  main.o(.data)
    blob_cy                                  0x200000b0   Data           4  main.o(.data)
    first_barr_count                         0x200000b4   Data           4  main.o(.data)
    test1_count                              0x200000b8   Data           4  main.o(.data)
    flag_findPoint                           0x200000bc   Data           1  main.o(.data)
    findPoint_time                           0x200000c0   Data           4  main.o(.data)
    flag_FindPoint_ForDir                    0x200000c4   Data           1  main.o(.data)
    key_flag                                 0x200000c5   Data           1  main.o(.data)
    find_Point_flag_WithKey                  0x200000c6   Data           1  main.o(.data)
    time_3000ms                              0x200000c8   Data           2  main.o(.data)
    time_3000ms_flag                         0x200000ca   Data           1  main.o(.data)
    extern_flag                              0x200000cb   Data           1  main.o(.data)
    time_1000ms                              0x200000cc   Data           2  main.o(.data)
    time_1000ms_flag                         0x200000ce   Data           1  main.o(.data)
    time_100ms                               0x200000d0   Data           2  main.o(.data)
    time_100ms_flag                          0x200000d2   Data           1  main.o(.data)
    count_TurnLeft                           0x200000d4   Data           4  main.o(.data)
    Flag_CountTurnLeft                       0x200000d8   Data           1  main.o(.data)
    uart1_rx_buf                             0x200000dc   Data          32  serial.o(.bss)
    __initial_sp                             0x20000500   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001578, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000149c, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          132    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         4108  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         4202    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         4205    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4207    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4209    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         4210    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         4217    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000144   0x08000144   0x00000000   Code   RO         4212    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000144   0x08000144   0x00000000   Code   RO         4214    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000144   0x08000144   0x00000004   Code   RO         4203    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000148   0x08000148   0x00000024   Code   RO          133    .text               startup_stm32f10x_hd.o
    0x0800016c   0x0800016c   0x000000b0   Code   RO         4145    .text               mf_w.l(fadd.o)
    0x0800021c   0x0800021c   0x00000064   Code   RO         4147    .text               mf_w.l(fmul.o)
    0x08000280   0x08000280   0x00000012   Code   RO         4155    .text               mf_w.l(fflti.o)
    0x08000292   0x08000292   0x00000032   Code   RO         4161    .text               mf_w.l(ffixi.o)
    0x080002c4   0x080002c4   0x00000014   Code   RO         4173    .text               mf_w.l(cfcmple.o)
    0x080002d8   0x080002d8   0x00000014   Code   RO         4175    .text               mf_w.l(cfrcmple.o)
    0x080002ec   0x080002ec   0x00000000   Code   RO         4236    .text               mc_w.l(iusefp.o)
    0x080002ec   0x080002ec   0x0000006e   Code   RO         4237    .text               mf_w.l(fepilogue.o)
    0x0800035a   0x0800035a   0x00000002   PAD
    0x0800035c   0x0800035c   0x00000024   Code   RO         4249    .text               mc_w.l(init.o)
    0x08000380   0x08000380   0x00000004   Code   RO         4035    i.BusFault_Handler  stm32f10x_it.o
    0x08000384   0x08000384   0x0000005c   Code   RO         3919    i.Control_Step      bjbj_driver.o
    0x080003e0   0x080003e0   0x00000002   Code   RO         4036    i.DebugMon_Handler  stm32f10x_it.o
    0x080003e2   0x080003e2   0x00000018   Code   RO         3196    i.Delay_ms          delay.o
    0x080003fa   0x080003fa   0x0000002e   Code   RO         3198    i.Delay_us          delay.o
    0x08000428   0x08000428   0x0000007c   Code   RO         3921    i.GPIO_Config       bjbj_driver.o
    0x080004a4   0x080004a4   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x080005ba   0x080005ba   0x00000002   PAD
    0x080005bc   0x080005bc   0x00000024   Code   RO         3952    i.GPIO_Init_key     main.o
    0x080005e0   0x080005e0   0x00000012   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080005f2   0x080005f2   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080005f6   0x080005f6   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080005fa   0x080005fa   0x00000004   Code   RO         4037    i.HardFault_Handler  stm32f10x_it.o
    0x080005fe   0x080005fe   0x00000004   Code   RO         3953    i.Key_read          main.o
    0x08000602   0x08000602   0x00000004   Code   RO         4038    i.MemManage_Handler  stm32f10x_it.o
    0x08000606   0x08000606   0x00000002   Code   RO         4039    i.NMI_Handler       stm32f10x_it.o
    0x08000608   0x08000608   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x08000678   0x08000678   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x0800068c   0x0800068c   0x00000084   Code   RO         3718    i.PID_Postion       pid.o
    0x08000710   0x08000710   0x00000114   Code   RO         3580    i.PWM_Init          pwm.o
    0x08000824   0x08000824   0x00000002   Code   RO         4040    i.PendSV_Handler    stm32f10x_it.o
    0x08000826   0x08000826   0x00000002   PAD
    0x08000828   0x08000828   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000848   0x08000848   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000868   0x08000868   0x000000d4   Code   RO         1785    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x0800093c   0x0800093c   0x00000002   Code   RO         4041    i.SVC_Handler       stm32f10x_it.o
    0x0800093e   0x0800093e   0x00000002   PAD
    0x08000940   0x08000940   0x000000b4   Code   RO         3632    i.Serial_Init       serial.o
    0x080009f4   0x080009f4   0x00000008   Code   RO           17    i.SetSysClock       system_stm32f10x.o
    0x080009fc   0x080009fc   0x000000e0   Code   RO           18    i.SetSysClockTo72   system_stm32f10x.o
    0x08000adc   0x08000adc   0x00000030   Code   RO         3922    i.SubdivisionSet    bjbj_driver.o
    0x08000b0c   0x08000b0c   0x00000002   Code   RO         4042    i.SysTick_Handler   stm32f10x_it.o
    0x08000b0e   0x08000b0e   0x00000002   PAD
    0x08000b10   0x08000b10   0x00000060   Code   RO           20    i.SystemInit        system_stm32f10x.o
    0x08000b70   0x08000b70   0x00000038   Code   RO         3954    i.TIM2_IRQHandler   main.o
    0x08000ba8   0x08000ba8   0x0000003c   Code   RO         3955    i.TIM3_IRQHandler   main.o
    0x08000be4   0x08000be4   0x00000130   Code   RO         3956    i.TIM6_IRQHandler   main.o
    0x08000d14   0x08000d14   0x00000018   Code   RO         2409    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08000d2c   0x08000d2c   0x00000006   Code   RO         2415    i.TIM_ClearFlag     stm32f10x_tim.o
    0x08000d32   0x08000d32   0x00000006   Code   RO         2416    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08000d38   0x08000d38   0x00000018   Code   RO         2421    i.TIM_Cmd           stm32f10x_tim.o
    0x08000d50   0x08000d50   0x00000022   Code   RO         2442    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08000d72   0x08000d72   0x00000012   Code   RO         2446    i.TIM_ITConfig      stm32f10x_tim.o
    0x08000d84   0x08000d84   0x00000098   Code   RO         2450    i.TIM_OC1Init       stm32f10x_tim.o
    0x08000e1c   0x08000e1c   0x00000012   Code   RO         2453    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x08000e2e   0x08000e2e   0x00000002   PAD
    0x08000e30   0x08000e30   0x000000a4   Code   RO         2492    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08000ed4   0x08000ed4   0x00000070   Code   RO         3221    i.Timer6_Init       timer.o
    0x08000f44   0x08000f44   0x00000110   Code   RO         3639    i.USART1_IRQHandler  serial.o
    0x08001054   0x08001054   0x0000001e   Code   RO         2957    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001072   0x08001072   0x00000018   Code   RO         2960    i.USART_Cmd         stm32f10x_usart.o
    0x0800108a   0x0800108a   0x00000054   Code   RO         2964    i.USART_GetITStatus  stm32f10x_usart.o
    0x080010de   0x080010de   0x0000004a   Code   RO         2966    i.USART_ITConfig    stm32f10x_usart.o
    0x08001128   0x08001128   0x000000d8   Code   RO         2967    i.USART_Init        stm32f10x_usart.o
    0x08001200   0x08001200   0x0000000a   Code   RO         2974    i.USART_ReceiveData  stm32f10x_usart.o
    0x0800120a   0x0800120a   0x00000004   Code   RO         4043    i.UsageFault_Handler  stm32f10x_it.o
    0x0800120e   0x0800120e   0x0000000e   Code   RO         4255    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800121c   0x0800121c   0x00000002   Code   RO         4256    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800121e   0x0800121e   0x0000000e   Code   RO         4257    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800122c   0x0800122c   0x0000000e   Code   RO         3957    i.fabs1             main.o
    0x0800123a   0x0800123a   0x00000002   PAD
    0x0800123c   0x0800123c   0x000000e8   Code   RO         3958    i.key_select        main.o
    0x08001324   0x08001324   0x00000158   Code   RO         3959    i.main              main.o
    0x0800147c   0x0800147c   0x00000020   Data   RO         4253    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800149c, Size: 0x00000500, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800149c   0x00000014   Data   RW         1805    .data               stm32f10x_rcc.o
    0x20000014   0x080014b0   0x00000008   Data   RW         3581    .data               pwm.o
    0x2000001c   0x080014b8   0x00000007   Data   RW         3643    .data               serial.o
    0x20000023   0x080014bf   0x00000001   PAD
    0x20000024   0x080014c0   0x000000b5   Data   RW         3962    .data               main.o
    0x200000d9   0x08001575   0x00000003   PAD
    0x200000dc        -       0x00000020   Zero   RW         3642    .bss                serial.o
    0x200000fc   0x08001575   0x00000004   PAD
    0x20000100        -       0x00000400   Zero   RW          130    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       264         24          0          0          0       2738   bjbj_driver.o
         0          0          0          0          0       4532   core_cm3.o
        70          0          0          0          0        954   delay.o
      1050        168          0        181          0       7842   main.o
       132         22          0          0          0     206207   misc.o
       132          0          0          0          0       1278   pid.o
       276         18          0          8          0        977   pwm.o
       452         40          0          7         32       2852   serial.o
        36          8        304          0       1024        780   startup_stm32f10x_hd.o
         0          0          0          0          0       1672   stm32f10x_adc.o
       304          0          0          0          0      11868   stm32f10x_gpio.o
        26          0          0          0          0       3854   stm32f10x_it.o
       276         32          0         20          0      13046   stm32f10x_rcc.o
       446         62          0          0          0      26058   stm32f10x_tim.o
       438          6          0          0          0      11488   stm32f10x_usart.o
       328         28          0          0          0      45321   system_stm32f10x.o
       112          6          0          0          0        558   timer.o

    ----------------------------------------------------------------------
      4354        <USER>        <GROUP>        220       1060     342025   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0          4          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
       176          0          0          0          0        140   fadd.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
       586         <USER>          <GROUP>          0          0        724   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        90         16          0          0          0         68   mc_w.l
       494          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
       586         <USER>          <GROUP>          0          0        724   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      4940        430        336        220       1060     340093   Grand Totals
      4940        430        336        220       1060     340093   ELF Image Totals
      4940        430        336        220          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 5276 (   5.15kB)
    Total RW  Size (RW Data + ZI Data)              1280 (   1.25kB)
    Total ROM Size (Code + RO Data + RW Data)       5496 (   5.37kB)

==============================================================================

